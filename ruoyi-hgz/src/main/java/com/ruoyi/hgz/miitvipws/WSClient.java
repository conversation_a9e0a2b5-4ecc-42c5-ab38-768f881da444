package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP_Service;

import javax.net.ssl.*;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Service;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

public class WSClient {
    private static final String WSDL_URL = WSClient.class.getResource("/com/ruoyi/hgz/miitvipws/CertificateRequestVIPService.wsdl").toString();
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";

    private static CertificateRequestVIP port;

    static {
        try {
            initSSL();
        } catch (Exception e) {
            throw new RuntimeException("初始化SSL失败", e);
        }
    }

    static void initSSL() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, new SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
    }

    public static synchronized CertificateRequestVIP getPort() {
        if (port == null) {
            try {
                URL wsdlUrl = new URL(WSDL_URL);
                QName qName = new QName(NAMESPACE_URI, SERVICE_NAME);
                CertificateRequestVIP_Service service = new CertificateRequestVIP_Service(wsdlUrl, qName);
                port = service.getCertificateRequestVIPServiceImplPort();

                BindingProvider bp = (BindingProvider) port;
                bp.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, SERVICE_URL);
            } catch (Exception e) {
                throw new RuntimeException("创建WebService客户端失败", e);
            }
        }
        return port;
    }


}