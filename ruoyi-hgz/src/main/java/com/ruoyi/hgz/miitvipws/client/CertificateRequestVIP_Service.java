
package com.ruoyi.hgz.miitvipws.client;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 *
 */
@WebServiceClient(name = "CertificateRequestVIP", targetNamespace = "http://www.vidc.info/certificate/operation/", wsdlLocation = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService?wsdl")
public class CertificateRequestVIP_Service
        extends Service
{

    private final static URL CERTIFICATEREQUESTVIP_WSDL_LOCATION;
    private final static WebServiceException CERTIFICATEREQUESTVIP_EXCEPTION;
    private final static QName CERTIFICATEREQUESTVIP_QNAME = new QName("http://www.vidc.info/certificate/operation/", "CertificateRequestVIP");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            // 首先尝试从本地资源加载WSDL
            url = CertificateRequestVIP_Service.class.getResource("/com/ruoyi/hgz/miitvipws/CertificateRequestVIPService.wsdl");
            if (url == null) {
                // 如果本地资源不存在，则尝试从远程URL加载
                url = new URL("https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService?wsdl");
            }
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        CERTIFICATEREQUESTVIP_WSDL_LOCATION = url;
        CERTIFICATEREQUESTVIP_EXCEPTION = e;
    }

    public CertificateRequestVIP_Service() {
        super(__getWsdlLocation(), CERTIFICATEREQUESTVIP_QNAME);
    }

    public CertificateRequestVIP_Service(WebServiceFeature... features) {
        super(__getWsdlLocation(), CERTIFICATEREQUESTVIP_QNAME, features);
    }

    public CertificateRequestVIP_Service(URL wsdlLocation) {
        super(wsdlLocation, CERTIFICATEREQUESTVIP_QNAME);
    }

    public CertificateRequestVIP_Service(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, CERTIFICATEREQUESTVIP_QNAME, features);
    }

    public CertificateRequestVIP_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public CertificateRequestVIP_Service(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     *
     * @return
     *     returns CertificateRequestVIP
     */
    @WebEndpoint(name = "CertificateRequestVIPServiceImplPort")
    public CertificateRequestVIP getCertificateRequestVIPServiceImplPort() {
        return super.getPort(new QName("http://www.vidc.info/certificate/operation/", "CertificateRequestVIPServiceImplPort"), CertificateRequestVIP.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns CertificateRequestVIP
     */
    @WebEndpoint(name = "CertificateRequestVIPServiceImplPort")
    public CertificateRequestVIP getCertificateRequestVIPServiceImplPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.vidc.info/certificate/operation/", "CertificateRequestVIPServiceImplPort"), CertificateRequestVIP.class, features);
    }

    private static URL __getWsdlLocation() {
        if (CERTIFICATEREQUESTVIP_EXCEPTION!= null) {
            throw CERTIFICATEREQUESTVIP_EXCEPTION;
        }
        return CERTIFICATEREQUESTVIP_WSDL_LOCATION;
    }

}
