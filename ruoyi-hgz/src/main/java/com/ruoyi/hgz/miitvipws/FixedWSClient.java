package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.ObjectFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 修复后的WebService客户端
 * 使用DynamicWSClient作为底层实现，但返回结构化的CertificateInfo对象
 * 解决了WSDL依赖问题，同时保持了便利的接口
 */
public class FixedWSClient {
    
    private static final ObjectFactory factory = new ObjectFactory();
    
    /**
     * 测试连接
     * @return 服务器响应消息
     */
    public static String helloWorld() {
        try {
            return DirectWSClient.helloWorld();
        } catch (Exception e) {
            throw new RuntimeException("HelloWorld调用失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询单个证书信息
     * 
     * @param username 用户名
     * @param password 密码
     * @param wzhgzbh 网证合格证编号（可为空）
     * @param clsbdh 车辆识别代号
     * @return 证书信息列表
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, 
                                                               String wzhgzbh, String clsbdh) {
        try {
            // 调用DynamicWSClient获取原始响应
            String rawResponse = DynamicWSClient.testQuery(username, password, wzhgzbh, clsbdh);
            
            // 解析响应并转换为CertificateInfo对象
            return parseResponseToCertificateInfo(rawResponse);
            
        } catch (Exception e) {
            throw new RuntimeException("查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 按条件查询证书信息
     * 
     * @param username 用户名
     * @param password 密码
     * @param wzhgzbh 网证合格证编号
     * @param clsbdh 车辆识别代号
     * @param clxh 车辆型号
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pagesite 页码
     * @param pageSize 页大小
     * @return 证书信息列表
     */
    public static List<CertificateInfo> queryByCondition(String username, String password,
                                                         String wzhgzbh, String clsbdh, String clxh,
                                                         String status, String startTime, String endTime,
                                                         int pagesite, int pageSize) {
        // 注意：DynamicWSClient目前只支持queryCertificateSingle方法
        // 这里暂时使用相同的实现，实际项目中需要扩展DynamicWSClient
        return queryCertificateSingle(username, password, wzhgzbh, clsbdh);
    }
    
    /**
     * 解析DynamicWSClient的响应为CertificateInfo对象列表
     */
    private static List<CertificateInfo> parseResponseToCertificateInfo(String rawResponse) {
        List<CertificateInfo> result = new ArrayList<>();
        
        if (rawResponse == null || rawResponse.trim().isEmpty() || rawResponse.contains("Error:")) {
            return result;
        }
        
        try {
            // 使用正则表达式解析响应
            CertificateInfo cert = parseAdvanced(rawResponse);
            if (cert != null) {
                result.add(cert);
            }
            
        } catch (Exception e) {
            System.err.println("解析响应失败: " + e.getMessage());
            // 如果高级解析失败，尝试简单解析
            try {
                CertificateInfo cert = parseSimple(rawResponse);
                if (cert != null) {
                    result.add(cert);
                }
            } catch (Exception e2) {
                System.err.println("简单解析也失败: " + e2.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 高级解析方法 - 使用正则表达式提取关键信息
     */
    private static CertificateInfo parseAdvanced(String rawResponse) {
        try {
            CertificateInfo cert = new CertificateInfo();
            boolean hasData = false;
            
            // 提取证书编号 (HIDC开头的编号)
            Pattern certPattern = Pattern.compile("(HIDC\\d+)");
            Matcher certMatcher = certPattern.matcher(rawResponse);
            if (certMatcher.find()) {
                cert.setWZHGZBH(factory.createCertificateInfoWZHGZBH(certMatcher.group(1)));
                hasData = true;
            }
            
            // 提取VIN码 (17位字母数字组合)
            Pattern vinPattern = Pattern.compile("([A-Z0-9]{17})");
            Matcher vinMatcher = vinPattern.matcher(rawResponse);
            if (vinMatcher.find()) {
                cert.setCLSBDH(factory.createCertificateInfoCLSBDH(vinMatcher.group(1)));
                hasData = true;
            }
            
            // 提取制造商信息 (包含"汽车"、"制造"、"有限公司"、"集团"等关键词的中文字符串)
            Pattern companyPattern = Pattern.compile("([\\u4e00-\\u9fa5]+(?:汽车|制造|有限公司|集团)[\\u4e00-\\u9fa5]*)");
            Matcher companyMatcher = companyPattern.matcher(rawResponse);
            if (companyMatcher.find()) {
                cert.setCLZZQYMC(factory.createCertificateInfoCLZZQYMC(companyMatcher.group(1)));
                hasData = true;
            }
            
            // 提取车辆类型
            Pattern typePattern = Pattern.compile("(专用汽车|载货汽车|客车|轿车|越野车)");
            Matcher typeMatcher = typePattern.matcher(rawResponse);
            if (typeMatcher.find()) {
                cert.setCLLX(factory.createCertificateInfoCLLX(typeMatcher.group(1)));
                hasData = true;
            }
            
            // 提取车辆名称
            Pattern namePattern = Pattern.compile("(工具车|货车|客车|轿车|[\\u4e00-\\u9fa5]{2,6}车)");
            Matcher nameMatcher = namePattern.matcher(rawResponse);
            if (nameMatcher.find()) {
                cert.setCLMC(factory.createCertificateInfoCLMC(nameMatcher.group(1)));
                hasData = true;
            }
            
            // 提取品牌 (包含"牌"字的中文字符串)
            Pattern brandPattern = Pattern.compile("([\\u4e00-\\u9fa5]+牌)");
            Matcher brandMatcher = brandPattern.matcher(rawResponse);
            if (brandMatcher.find()) {
                cert.setCLPP(factory.createCertificateInfoCLPP(brandMatcher.group(1)));
                hasData = true;
            }
            
            // 提取车辆型号 (字母数字组合，通常在品牌后面)
            Pattern modelPattern = Pattern.compile("([A-Z]+\\d+[A-Z]*\\d*[A-Z]*)");
            Matcher modelMatcher = modelPattern.matcher(rawResponse);
            if (modelMatcher.find()) {
                String model = modelMatcher.group(1);
                // 排除VIN码
                if (!model.matches("[A-Z0-9]{17}")) {
                    cert.setCLXH(factory.createCertificateInfoCLXH(model));
                    hasData = true;
                }
            }
            
            // 提取颜色
            Pattern colorPattern = Pattern.compile("(红|蓝|白|黑|灰|银|绿|黄|橙|紫|棕)");
            Matcher colorMatcher = colorPattern.matcher(rawResponse);
            if (colorMatcher.find()) {
                cert.setCSYS(factory.createCertificateInfoCSYS(colorMatcher.group(1)));
                hasData = true;
            }
            
            return hasData ? cert : null;
            
        } catch (Exception e) {
            System.err.println("高级解析失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 简单解析方法 - 按空格分割字段
     */
    private static CertificateInfo parseSimple(String rawResponse) {
        try {
            String[] parts = rawResponse.trim().split("\\s+");
            
            if (parts.length < 3) {
                return null;
            }
            
            CertificateInfo cert = new CertificateInfo();
            boolean hasData = false;
            
            // 查找证书编号
            for (String part : parts) {
                if (part.startsWith("HIDC") && part.length() > 10) {
                    cert.setWZHGZBH(factory.createCertificateInfoWZHGZBH(part));
                    hasData = true;
                    break;
                }
            }
            
            // 查找VIN码
            for (String part : parts) {
                if (part.length() == 17 && part.matches("[A-Z0-9]{17}")) {
                    cert.setCLSBDH(factory.createCertificateInfoCLSBDH(part));
                    hasData = true;
                    break;
                }
            }
            
            // 查找制造商（通常是第三个字段，包含中文）
            for (String part : parts) {
                if (part.matches(".*[\\u4e00-\\u9fa5].*") && part.length() > 3) {
                    cert.setCLZZQYMC(factory.createCertificateInfoCLZZQYMC(part));
                    hasData = true;
                    break;
                }
            }
            
            return hasData ? cert : null;
            
        } catch (Exception e) {
            System.err.println("简单解析失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 重置连接
     */
    public static void resetConnection() {
        DynamicWSClient.resetConnection();
    }
    
    /**
     * 获取服务信息
     */
    public static String getServiceInfo() {
        return "FixedWSClient - 基于DynamicWSClient的结构化WebService客户端\n" +
               "解决了WSDL依赖问题，返回CertificateInfo对象\n" +
               "底层使用SOAP消息直接调用";
    }
    
    /**
     * 验证连接是否正常
     */
    public static boolean validateConnection() {
        try {
            String result = helloWorld();
            return result != null && result.contains("Hello");
        } catch (Exception e) {
            return false;
        }
    }
}
