
package com.ruoyi.hgz.miitvipws.client;

import java.util.List;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "CertificateRequestVIP", targetNamespace = "http://www.vidc.info/certificate/operation/", wsdlLocation = "/com/ruoyi/hgz/miitvipws/CertificateRequestVIPService.wsdl")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface CertificateRequestVIP {


    /**
     * 
     * @param wzhgzbh
     * @param password
     * @param clsbdh
     * @param username
     * @return
     *     returns java.util.List<com.ruoyi.hgz.miitvipws.client.WzHgzInfo>
     */
    @WebMethod(operationName = "QueryCertificateSingle", action = "http://www.vidc.info/certificate/operation/QueryCertificateSingle")
    @WebResult(name = "QueryCertificateSingleResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "QueryCertificateSingle", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.QueryCertificateSingle")
    @ResponseWrapper(localName = "QueryCertificateSingleResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.QueryCertificateSingleResponse")
    public List<CertificateInfo> queryCertificateSingle(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "wzhgzbh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String wzhgzbh,
        @WebParam(name = "clsbdh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String clsbdh);

    /**
     * 
     * @param wzhgzbh
     * @param password
     * @param pageSize
     * @param startTime
     * @param clsbdh
     * @param endTime
     * @param applicType
     * @param pagesite
     * @param username
     * @param status
     * @return
     *     returns java.util.List<com.ruoyi.hgz.miitvipws.client.WzHgzInfo>
     */
    @WebMethod(operationName = "QueryHisByCondition", action = "http://www.vidc.info/certificate/operation/QueryHisByCondition")
    @WebResult(name = "QueryHisByConditionResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "QueryHisByCondition", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.QueryHisByCondition")
    @ResponseWrapper(localName = "QueryHisByConditionResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.QueryHisByConditionResponse")
    public List<CertificateInfo> queryHisByCondition(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "wzhgzbh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String wzhgzbh,
        @WebParam(name = "clsbdh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String clsbdh,
        @WebParam(name = "startTime", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String startTime,
        @WebParam(name = "endTime", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String endTime,
        @WebParam(name = "applicType", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String applicType,
        @WebParam(name = "status", targetNamespace = "http://www.vidc.info/certificate/operation/")
        int status,
        @WebParam(name = "pagesite", targetNamespace = "http://www.vidc.info/certificate/operation/")
        int pagesite,
        @WebParam(name = "pageSize", targetNamespace = "http://www.vidc.info/certificate/operation/")
        int pageSize);

    /**
     * 
     * @param password
     * @param memo
     * @param certificateInfos
     * @param ukey
     * @param username
     * @return
     *     returns com.ruoyi.hgz.miitvipws.client.OperateResult
     */
    @WebMethod(operationName = "UploadOverTime_Ent", action = "http://www.vidc.info/certificate/operation/UploadOverTime_Ent")
    @WebResult(name = "UploadOverTime_EntResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "UploadOverTime_Ent", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadOverTimeEnt")
    @ResponseWrapper(localName = "UploadOverTime_EntResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadOverTimeEntResponse")
    public OperateResult uploadOverTimeEnt(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "certificateInfos", targetNamespace = "http://www.vidc.info/certificate/operation/")
        List<CertificateInfo> certificateInfos,
        @WebParam(name = "memo", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String memo,
        @WebParam(name = "ukey", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String ukey);

    /**
     * 
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "HelloWorld", action = "http://www.vidc.info/certificate/operation/HelloWorld")
    @WebResult(name = "HelloWorldResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "HelloWorld", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.HelloWorld")
    @ResponseWrapper(localName = "HelloWorldResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.HelloWorldResponse")
    public String helloWorld();

    /**
     * 
     * @param password
     * @param memo
     * @param certificateInfos
     * @param ukey
     * @param username
     * @return
     *     returns com.ruoyi.hgz.miitvipws.client.OperateResult
     */
    @WebMethod(operationName = "UploadDelete_Ent", action = "http://www.vidc.info/certificate/operation/UploadDelete_Ent")
    @WebResult(name = "UploadDelete_EntResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "UploadDelete_Ent", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadDeleteEnt")
    @ResponseWrapper(localName = "UploadDelete_EntResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadDeleteEntResponse")
    public OperateResult uploadDeleteEnt(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "certificateInfos", targetNamespace = "http://www.vidc.info/certificate/operation/")
        List<CertificateInfo> certificateInfos,
        @WebParam(name = "memo", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String memo,
        @WebParam(name = "ukey", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String ukey);

    /**
     * 
     * @param password
     * @param memo
     * @param certificateInfos
     * @param ukey
     * @param username
     * @return
     *     returns com.ruoyi.hgz.miitvipws.client.OperateResult
     */
    @WebMethod(operationName = "UploadUpdate_EntEX", action = "http://www.vidc.info/certificate/operation/UploadUpdate_EntEX")
    @WebResult(name = "UploadUpdate_EntEXResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "UploadUpdate_EntEX", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadUpdateEntEX")
    @ResponseWrapper(localName = "UploadUpdate_EntEXResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadUpdateEntEXResponse")
    public OperateResult uploadUpdateEntEX(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "certificateInfos", targetNamespace = "http://www.vidc.info/certificate/operation/")
        List<CertificateInfo> certificateInfos,
        @WebParam(name = "memo", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String memo,
        @WebParam(name = "ukey", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String ukey);

    /**
     * 
     * @param password
     * @param pc
     * @param clxh
     * @param cph
     * @param username
     * @return
     *     returns byte[]
     */
    @WebMethod(operationName = "GetAfficheZC", action = "http://www.vidc.info/certificate/operation/GetAfficheZC")
    @WebResult(name = "GetAfficheZCResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "GetAfficheZC", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.GetAfficheZC")
    @ResponseWrapper(localName = "GetAfficheZCResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.GetAfficheZCResponse")
    public byte[] getAfficheZC(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "pc", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String pc,
        @WebParam(name = "cph", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String cph,
        @WebParam(name = "clxh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String clxh);

    /**
     * 
     * @param password
     * @param certificateInfos
     * @param ukey
     * @param username
     * @return
     *     returns com.ruoyi.hgz.miitvipws.client.OperateResult
     */
    @WebMethod(operationName = "UploadInser_Ent", action = "http://www.vidc.info/certificate/operation/UploadInser_Ent")
    @WebResult(name = "UploadInser_EntResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "UploadInser_Ent", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadInserEnt")
    @ResponseWrapper(localName = "UploadInser_EntResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadInserEntResponse")
    public OperateResult uploadInserEnt(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "certificateInfos", targetNamespace = "http://www.vidc.info/certificate/operation/")
        List<CertificateInfo> certificateInfos,
        @WebParam(name = "ukey", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String ukey);

    /**
     * 
     * @param password
     * @param pc
     * @param dpxh
     * @param cph
     * @param username
     * @return
     *     returns byte[]
     */
    @WebMethod(operationName = "GetAfficheDP", action = "http://www.vidc.info/certificate/operation/GetAfficheDP")
    @WebResult(name = "GetAfficheDPResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "GetAfficheDP", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.GetAfficheDP")
    @ResponseWrapper(localName = "GetAfficheDPResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.GetAfficheDPResponse")
    public byte[] getAfficheDP(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "pc", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String pc,
        @WebParam(name = "cph", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String cph,
        @WebParam(name = "dpxh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String dpxh);

    /**
     * 
     * @param wzhgzbh
     * @param password
     * @param pageSize
     * @param startTime
     * @param clsbdh
     * @param endTime
     * @param applicType
     * @param pagesite
     * @param username
     * @param status
     * @return
     *     returns java.util.List<com.ruoyi.hgz.miitvipws.client.WzHgzInfo>
     */
    @WebMethod(operationName = "QueryOnWayByCondition", action = "http://www.vidc.info/certificate/operation/QueryOnWayByCondition")
    @WebResult(name = "QueryOnWayByConditionResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "QueryOnWayByCondition", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.QueryOnWayByCondition")
    @ResponseWrapper(localName = "QueryOnWayByConditionResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.QueryOnWayByConditionResponse")
    public List<CertificateInfo> queryOnWayByCondition(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "wzhgzbh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String wzhgzbh,
        @WebParam(name = "clsbdh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String clsbdh,
        @WebParam(name = "startTime", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String startTime,
        @WebParam(name = "endTime", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String endTime,
        @WebParam(name = "applicType", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String applicType,
        @WebParam(name = "status", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String status,
        @WebParam(name = "pagesite", targetNamespace = "http://www.vidc.info/certificate/operation/")
        int pagesite,
        @WebParam(name = "pageSize", targetNamespace = "http://www.vidc.info/certificate/operation/")
        int pageSize);

    /**
     * 
     * @param wzhgzbh
     * @param password
     * @param clxh
     * @param pageSize
     * @param startTime
     * @param clsbdh
     * @param endTime
     * @param pagesite
     * @param username
     * @param status
     * @return
     *     returns java.util.List<com.ruoyi.hgz.miitvipws.client.WzHgzInfo>
     */
    @WebMethod(operationName = "QueryByCondition", action = "http://www.vidc.info/certificate/operation/QueryByCondition")
    @WebResult(name = "QueryByConditionResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "QueryByCondition", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.QueryByCondition")
    @ResponseWrapper(localName = "QueryByConditionResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.QueryByConditionResponse")
    public List<CertificateInfo> queryByCondition(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "wzhgzbh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String wzhgzbh,
        @WebParam(name = "clsbdh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String clsbdh,
        @WebParam(name = "clxh", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String clxh,
        @WebParam(name = "status", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String status,
        @WebParam(name = "startTime", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String startTime,
        @WebParam(name = "endTime", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String endTime,
        @WebParam(name = "pagesite", targetNamespace = "http://www.vidc.info/certificate/operation/")
        int pagesite,
        @WebParam(name = "pageSize", targetNamespace = "http://www.vidc.info/certificate/operation/")
        int pageSize);

    /**
     * 
     * @param password
     * @param memo
     * @param certificateInfos
     * @param ukey
     * @param username
     * @return
     *     returns com.ruoyi.hgz.miitvipws.client.OperateResult
     */
    @WebMethod(operationName = "UploadUpdate_Ent", action = "http://www.vidc.info/certificate/operation/UploadUpdate_Ent")
    @WebResult(name = "UploadUpdate_EntResult", targetNamespace = "http://www.vidc.info/certificate/operation/")
    @RequestWrapper(localName = "UploadUpdate_Ent", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadUpdateEnt")
    @ResponseWrapper(localName = "UploadUpdate_EntResponse", targetNamespace = "http://www.vidc.info/certificate/operation/", className = "com.ruoyi.hgz.miitvipws.client.UploadUpdateEntResponse")
    public OperateResult uploadUpdateEnt(
        @WebParam(name = "username", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String username,
        @WebParam(name = "password", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String password,
        @WebParam(name = "certificateInfos", targetNamespace = "http://www.vidc.info/certificate/operation/")
        List<CertificateInfo> certificateInfos,
        @WebParam(name = "memo", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String memo,
        @WebParam(name = "ukey", targetNamespace = "http://www.vidc.info/certificate/operation/")
        String ukey);

}
