package com.ruoyi.hgz.service.impl;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.hgz.domain.HgzInfo;
import com.ruoyi.hgz.domain.WzHgzInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;
import com.ruoyi.hgz.service.IHgzToolService;
import com.ruoyi.hgz.utils.HgzCheckUtils;
import com.ruoyi.hgz.utils.HgzConvertUtils;
import com.ruoyi.hgz.utils.HgzQRUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.ruoyi.common.utils.DictUtils.getSysConfigCacheKey;
import static com.ruoyi.hgz.miitvipws.WSClient.getPort;
import static java.lang.System.out;


@Service
public class HgzToolServiceImpl implements IHgzToolService
{
    @Autowired
    private RedisCache redisCache;

    @Value("${hgz.ycyh}")
    private String ycyh;

    @Value("${hgz.ycmm}")
    private String ycmm;

    /*
     * 二维码解密
     * @param qrcode 二维码
     * @return HgzInfo
     */
    @Override
    public HgzInfo decryptQRCode(String qrcode) {
        //获取二维码解密内容
        String qrresult= HgzQRUtils.getInstance().decryptData(qrcode);
        //对解密内容按|分割
        String[] qrresults = qrresult.split("\\|");
        //获取分割后的第2个内容，判断是QX整车还是DP底盘
        String qrresult2 = qrresults[1];
        HgzInfo hgzinfo = new HgzInfo();

        hgzinfo.setQrcodeResult(qrresult);
        //如果是QX整车，按整车进行解析
        if (qrresult2.startsWith("QX")) {
            //解析整车信息
            hgzinfo.setHgzfl(qrresults[1]);
            hgzinfo.setHgzbh(qrresults[2]);
            hgzinfo.setDphgzbh(qrresults[13]);
            hgzinfo.setVin(qrresults[14]);
            hgzinfo.setClxh(qrresults[9]);
            hgzinfo.setClmc(qrresults[7]);
            hgzinfo.setFzrq(qrresults[3]);
            hgzinfo.setScrq(qrresults[50]);
            hgzinfo.setQymc(qrresults[4]);
            hgzinfo.setPp(qrresults[8]);
            hgzinfo.setPh(qrresults[61]);
        }
        //如果是DP底盘，按底盘进行解析
        else if (qrresult2.startsWith("DP")) {
            //解析底盘信息
            hgzinfo.setHgzfl(qrresults[1]);
            hgzinfo.setHgzbh(qrresults[2]);
            hgzinfo.setDphgzbh(qrresults[13]);
            hgzinfo.setVin(qrresults[14]);
            hgzinfo.setClxh(qrresults[9]);
            hgzinfo.setClmc(qrresults[7]);
            hgzinfo.setFzrq(qrresults[3]);
            hgzinfo.setScrq(qrresults[50]);
            hgzinfo.setQymc(qrresults[4]);
            hgzinfo.setPp(qrresults[8]);
            hgzinfo.setPh(qrresults[61]);
        }
        return hgzinfo;
    }

    /*
     * 二维码校验
     * @param qrcode 二维码
     * @return boolean
     */
    @Override
    public boolean checkQRCode(String qrcode) {

        // 判断是否为空
        if (StringUtils.isEmpty(qrcode)) {
            //"待解析数据为空";
            return false;
        }
        // 判断是否以ZCCCHGZ开头
        if (!qrcode.startsWith("ZCCCHGZ")) {
            //"待解析数据格式不正确:文件头不正确";
            return false;
        }
        // 截取第一个#和第一个|之间的内容是一个数字，该数字为第一个|后的字符串长度，判断是否一致
        String[] parts = qrcode.split("#");
        if (parts.length < 2) {
            //"待解析数据格式不正确:数据缺失#";
            return false;
        }
        String[] parts2 = parts[1].split("\\|");
        if (parts2.length < 2) {
            //"待解析数据格式不正确:数据缺失|";
            return false;
        }
        String lengthStr = parts2[0];
        String dataStr = parts2[1];
        int length = Integer.parseInt(lengthStr);
        if (length != dataStr.length()) {
            //"待解析数据格式不正确:数据长度不正确";
            return false;
        }
        return true;
    }

    /*
     * 二维码次数限制
     * @return boolean
     */
    @Override
    public boolean descQrCodeLimit() {
        // 先获取当前值
        Long configValue = redisCache.decrCacheObject(getSysConfigCacheKey("hgz.desqrcode.limit"));
        // 如果当前值大于0，则返回true
        if (configValue >= 0) {
            return true;
        }
        // 如果小于0，则返回false
        return false;

    }

    /*
     * 合格证编号校验
     * @param hgzbh 合格证编号
     * @return boolean
     */
    @Override
    public boolean checkHgzbh(String hgzbh) {
        //hgzbh需要为15位数字或大写字母
        if (StringUtils.isEmpty(hgzbh)) {
            //"合格证编号不能为空";
            return false;
        }
        if (hgzbh.length() != 15) {
            //"合格证编号长度不正确";
            return false;
        }
        if (!hgzbh.matches("^[A-Z0-9]+$")) {
            //"合格证编号格式不正确";
            return false;
        }
        // 使用合格证校验工具验证编号
        return HgzCheckUtils.verifyCert(hgzbh);
    }

    /*
     * 合格证编号查询
     * @param wzhgzbh 合格证编号
     * @return WzHgzInfo
     */
    @Override
    public WzHgzInfo getWzHgzInfo(String wzhgzbh) {
        try {
            CertificateRequestVIP port = getPort();
            List<CertificateInfo> list = port.queryCertificateSingle(ycyh, ycmm, wzhgzbh, "");
            
            if (list.size() > 0) {
                CertificateInfo certificateInfo = list.get(0);
                return HgzConvertUtils.convertToWzHgzInfo(certificateInfo);
            }
            WzHgzInfo wzHgzInfo = new WzHgzInfo();
            wzHgzInfo.setMsg("【"+wzhgzbh+"】在国家不存在");
            return wzHgzInfo;
        } catch (Exception e) {
            WzHgzInfo wzHgzInfo = new WzHgzInfo();
            wzHgzInfo.setMsg("国家接口网络连接异常，请稍后重试");
            return wzHgzInfo;
        }
    }

    /*
     * 合格证编号次数限制
     * @return boolean
     */
    @Override
    public boolean getWzHgzInfoLimit() {
        // 先获取当前值
        Long configValue = redisCache.decrCacheObject(getSysConfigCacheKey("hgz.wzhgzinfo.limit"));
        // 如果当前值大于0，则返回true
        if (configValue >= 0) {
            return true;
        }
        // 如果小于0，则返回false
        return false;
    }

}
